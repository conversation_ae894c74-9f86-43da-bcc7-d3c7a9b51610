<script setup lang="ts">
import {
  saveSystemChannelWarehouse,
  updateSystemChannelWarehouse,
  findInfoSystemChannelWarehouse
} from '@/api/channelEntity'
import type { SystemChannelWarehouseModel } from '@/api/channelEntity/index.d'

defineOptions({
  name: 'AddAddress'
})

const visible = defineModel({ default: false })

const props = defineProps<{
  editRecord?: SystemChannelWarehouseModel | null
  systemChannelId?: string
}>()

const emit = defineEmits<{
  success: []
}>()

const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref<SystemChannelWarehouseModel>({
  id: undefined,
  systemChannelId: props.systemChannelId,
  warehouseId: undefined,
  addressName: '',
  warehouseName: '',
  warehouseCode: '',
  warehouseType: '',
  contacts: '',
  contactsPhone: '',
  contactsEmail: '',
  companyName: '',
  postalCode: '',
  country: '',
  countryCode: '',
  province: '',
  provinceCode: '',
  city: '',
  cityCode: '',
  detailedAddressOne: '',
  detailedAddressTwo: '',
  status: '1'
})

// 表单验证规则
const rules = ref({
  addressName: [{ required: true, message: '请输入地址名称', trigger: 'blur' }],
  warehouseName: [
    { required: true, message: '请输入仓库名称', trigger: 'blur' }
  ],
  contacts: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactsPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  country: [{ required: true, message: '请选择国家/地区', trigger: 'change' }],
  city: [{ required: true, message: '请输入城市', trigger: 'blur' }]
})

const isEdit = computed(() => !!props.editRecord?.id)

// 监听编辑记录变化
watch(
  () => props.editRecord,
  newRecord => {
    if (newRecord && visible.value) {
      loadEditData(newRecord.id!)
    }
  },
  { immediate: true }
)

const loadEditData = async (id: string) => {
  try {
    loading.value = true
    const res = await findInfoSystemChannelWarehouse({ id })
    if (res.result) {
      Object.assign(formData.value, res.result)
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  if (!isEdit.value) {
    // 新增时重置表单
    formData.value = {
      id: undefined,
      systemChannelId: props.systemChannelId,
      warehouseId: undefined,
      addressName: '',
      warehouseName: '',
      warehouseCode: '',
      warehouseType: '',
      contacts: '',
      contactsPhone: '',
      contactsEmail: '',
      companyName: '',
      postalCode: '',
      country: '',
      countryCode: '',
      province: '',
      provinceCode: '',
      city: '',
      cityCode: '',
      detailedAddressOne: '',
      detailedAddressTwo: '',
      status: '1'
    }
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const apiCall = isEdit.value
      ? updateSystemChannelWarehouse
      : saveSystemChannelWarehouse
    await apiCall(formData.value)

    ElMessage.success(isEdit.value ? '修改成功' : '添加成功')
    visible.value = false
    emit('success')
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error(isEdit.value ? '修改失败' : '添加失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑发货地址' : '添加发货地址'"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      v-loading="loading"
      :model="formData"
      :rules="rules"
      ref="formRef"
      label-position="left"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="16" class="w-full">
        <el-col :span="12">
          <el-form-item label="地址名称" prop="addressName">
            <el-input
              v-model.trim="formData.addressName"
              clearable
              placeholder="请输入地址名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="仓库名称" prop="warehouseName">
            <el-input
              v-model.trim="formData.warehouseName"
              clearable
              placeholder="请输入仓库名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="仓库代码" prop="warehouseCode">
            <el-input
              v-model.trim="formData.warehouseCode"
              clearable
              placeholder="请输入仓库代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="仓库类型" prop="warehouseType">
            <el-input
              v-model.trim="formData.warehouseType"
              clearable
              placeholder="请输入仓库类型"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="contacts">
            <el-input
              v-model.trim="formData.contacts"
              clearable
              placeholder="请输入联系人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactsPhone">
            <el-input
              v-model.trim="formData.contactsPhone"
              clearable
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="contactsEmail">
            <el-input
              v-model.trim="formData.contactsEmail"
              clearable
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司名称" prop="companyName">
            <el-input
              v-model.trim="formData.companyName"
              clearable
              placeholder="请输入公司名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国家" prop="country">
            <el-input
              v-model.trim="formData.country"
              clearable
              placeholder="请输入国家"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国家代码" prop="countryCode">
            <el-input
              v-model.trim="formData.countryCode"
              clearable
              placeholder="请输入国家代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="省/州" prop="province">
            <el-input
              v-model.trim="formData.province"
              clearable
              placeholder="请输入省/州"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="省/州代码" prop="provinceCode">
            <el-input
              v-model.trim="formData.provinceCode"
              clearable
              placeholder="请输入省/州代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="城市" prop="city">
            <el-input
              v-model.trim="formData.city"
              clearable
              placeholder="请输入城市"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="城市代码" prop="cityCode">
            <el-input
              v-model.trim="formData.cityCode"
              clearable
              placeholder="请输入城市代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮编" prop="postalCode">
            <el-input
              v-model.trim="formData.postalCode"
              clearable
              placeholder="请输入邮编"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务商地址代码" prop="serviceAddressCode">
            <el-input
              v-model.trim="formData.serviceAddressCode"
              clearable
              placeholder="请输入服务商地址代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="详细地址1" prop="detailedAddressOne">
            <el-input
              v-model.trim="formData.detailedAddressOne"
              clearable
              placeholder="请输入详细地址1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="详细地址2" prop="detailedAddressTwo">
            <el-input
              v-model.trim="formData.detailedAddressTwo"
              clearable
              placeholder="请输入详细地址2（可选）"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
