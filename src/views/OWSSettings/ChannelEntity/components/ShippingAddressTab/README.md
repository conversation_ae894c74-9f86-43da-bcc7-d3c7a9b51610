# 系统渠道绑定仓库地址管理

## 功能概述

本模块实现了系统渠道绑定仓库地址的完整增删改查功能，包括：

- 地址列表展示
- 添加新地址
- 编辑现有地址
- 删除地址（软删除，通过状态更新实现）
- 分页查询

## 文件结构

```
ShippingAddressTab/
├── index.vue          # 主列表组件
├── AddAddress.vue     # 添加/编辑地址弹窗组件
└── README.md          # 说明文档
```

## API接口映射

使用 `src/api/channelEntity/index.ts` 中的以下接口：

- `findListSystemChannelWarehouse` - 分页查询地址列表
- `saveSystemChannelWarehouse` - 保存新地址
- `updateSystemChannelWarehouse` - 更新地址信息
- `findInfoSystemChannelWarehouse` - 根据ID查询地址详情
- `changeStatusSystemChannelWarehouse` - 更新地址状态（用于软删除）

## 数据字段映射

### 列表显示字段
- `addressName` - 地址名称
- `warehouseName` - 仓库名称
- `warehouseCode` - 仓库代码
- `contacts` - 联系人
- `contactsPhone` - 联系电话
- `contactsEmail` - 邮箱
- `companyName` - 公司名称
- `country` - 国家
- `province` - 省/州
- `city` - 城市
- `postalCode` - 邮编
- `status` - 状态（1-启用，0-禁用）

### 表单字段
- 基本信息：地址名称、仓库名称、仓库代码、仓库类型
- 联系信息：联系人、联系电话、邮箱、公司名称
- 地址信息：国家、国家代码、省/州、省/州代码、城市、城市代码、邮编
- 详细地址：详细地址1、详细地址2、服务商地址代码

## 使用方法

### 在父组件中使用

```vue
<template>
  <ShippingAddressTab :system-channel-id="channelId" />
</template>

<script setup lang="ts">
import ShippingAddressTab from './components/ShippingAddressTab/index.vue'

const channelId = ref('your-channel-id')
</script>
```

### Props

- `systemChannelId` - 系统渠道ID，用于关联地址数据

## 功能特性

1. **响应式设计** - 支持不同屏幕尺寸
2. **表单验证** - 必填字段验证
3. **加载状态** - 操作过程中显示加载状态
4. **错误处理** - 完善的错误提示
5. **状态管理** - 启用/禁用状态显示
6. **分页支持** - 支持大量数据分页展示

## 注意事项

1. 删除操作实际上是软删除，通过将状态设置为'0'实现
2. 编辑功能会先加载完整的地址信息
3. 所有API调用都包含错误处理
4. 表单验证确保数据完整性
