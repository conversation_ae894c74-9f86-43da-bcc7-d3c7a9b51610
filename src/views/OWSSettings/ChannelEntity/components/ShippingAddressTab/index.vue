<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  findListSystemChannelWarehouse,
  changeStatusSystemChannelWarehouse
} from '@/api/channelEntity'
import type { SystemChannelWarehouseVo } from '@/api/channelEntity/index.d'
import { useToggle } from '@/hooks/useToggle'
import AddAddress from './AddAddress.vue'

defineOptions({
  name: 'ShippingAddressTab'
})

// 接收父组件传递的系统渠道ID
const props = defineProps<{
  systemChannelId?: string
}>()

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods
} = useTable<SystemChannelWarehouseVo>({
  immediate: true,
  initialFormData: {
    systemChannelId: props.systemChannelId
  },
  fetchDataApi: async () => {
    const res = await findListSystemChannelWarehouse({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      systemChannelId: props.systemChannelId
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      // 批量删除：将状态设置为禁用
      const promises = record.map((item: any) =>
        changeStatusSystemChannelWarehouse({
          id: item.id,
          status: '0' // 假设0表示禁用状态
        })
      )
      const results = await Promise.all(promises)
      return results.every(res => !!res)
    } else {
      // 单个删除：将状态设置为禁用
      const res = await changeStatusSystemChannelWarehouse({
        id: record.id,
        status: '0'
      })
      return !!res
    }
  }
})

const tableRef = ref()
const [acVisible, handleAc] = useToggle()

// 编辑地址
const currentEditRecord = ref<SystemChannelWarehouseVo | null>(null)

const handleEdit = (record: SystemChannelWarehouseVo) => {
  currentEditRecord.value = record
  handleAc()
}

const handleDelete = async (record: SystemChannelWarehouseVo) => {
  tableMethods.hadnleDel(record)
}

// 处理添加/编辑完成后的刷新
const handleAddressChange = () => {
  currentEditRecord.value = null
  tableMethods.refresh()
}
</script>

<template>
  <div>
    <div class="mb-18">
      <el-button @click="() => handleAc()">添加地址</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column label="地址名称" prop="addressName" />
      <el-table-column label="仓库名称" prop="warehouseName" />
      <el-table-column label="仓库代码" prop="warehouseCode" width="120" />
      <el-table-column label="联系人" prop="contacts" width="100" />
      <el-table-column label="联系电话" prop="contactsPhone" width="130" />
      <el-table-column label="邮箱" prop="contactsEmail" width="180" />
      <el-table-column label="公司名称" prop="companyName" width="150" />
      <el-table-column label="国家" prop="country" width="100" />
      <el-table-column label="省/州" prop="province" width="100" />
      <el-table-column label="城市" prop="city" width="100" />
      <el-table-column label="邮编" prop="postalCode" width="100" />
      <el-table-column label="状态" prop="status" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === '1' ? 'success' : 'danger'">
            {{ row.status === '1' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="160">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)"
            >编辑</el-button
          >
          <el-button link type="danger" @click="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />

    <AddAddress
      v-model="acVisible"
      :edit-record="currentEditRecord"
      :system-channel-id="props.systemChannelId"
      @success="handleAddressChange"
    />
  </div>
</template>

<style lang="scss" scoped></style>
